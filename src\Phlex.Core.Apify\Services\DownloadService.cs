﻿using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Constants;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using System.Text;
using System.Text.Json;
using System.Web;

namespace Phlex.Core.Apify.Services;

public class DownloadService(ILogger<DownloadService> logger) : IDownloadService
{
    private const string KeyPlaceholder = "##key##";
    private const string JsonFileExtension = ".json";
    private const string FileNameSeparator = "_";

    public async Task BinaryDownloadAsync(HttpClient client, string requestTemplateUrl, string[] keys, IDownloadStorage storage, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentException.ThrowIfNullOrWhiteSpace(requestTemplateUrl);
        ArgumentNullException.ThrowIfNull(keys);
        ArgumentNullException.ThrowIfNull(storage);

        if (!requestTemplateUrl.Contains(KeyPlaceholder))
        {
            throw new ArgumentException($"Request template URL must contain the placeholder '{KeyPlaceholder}'", nameof(requestTemplateUrl));
        }

        var validKeys = keys.Where(key => !string.IsNullOrWhiteSpace(key) &&
                                         !DownloadConstants.ExcludeFiles.Any(excludeFile =>
                                             key.Contains(excludeFile, StringComparison.OrdinalIgnoreCase)))
                             .ToArray();

        if (validKeys.Length == 0)
        {
            logger.LogInformation("Apify: No valid keys found for binary download");
            return;
        }

        logger.LogInformation("Apify: Starting binary download for {KeyCount} keys", validKeys.Length);

        var downloadTasks = validKeys.Select(async key =>
        {
            try
            {
                var requestUrl = requestTemplateUrl.Replace(KeyPlaceholder, key);
                using var stream = await client.GetStreamAsync(requestUrl, cancellationToken);
                var encodedKey = HttpUtility.UrlEncode(key);
                await storage.WriteDataItemAsync(encodedKey, stream, cancellationToken);

                logger.LogDebug("Apify: Successfully downloaded binary data for key: {Key}", key);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Apify: Failed to download binary data for key: {Key}", key);
                throw; // Re-throw to allow caller to handle the error appropriately
            }
        });

        try
        {
            await Task.WhenAll(downloadTasks);
            logger.LogInformation("Apify: Binary download completed successfully for {KeyCount} keys", validKeys.Length);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Apify: Binary download failed with error: {ErrorMessage}", ex.Message);
            throw;
        }
    }

    public async Task DownloadItemsAsync(string dataSetId, List<object> items, IDownloadStorage storage, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(dataSetId);
        ArgumentNullException.ThrowIfNull(items);
        ArgumentNullException.ThrowIfNull(storage);

        if (items.Count == 0)
        {
            logger.LogInformation("Apify: No items to download for dataset: {DataSetId}", dataSetId);
            return;
        }

        logger.LogInformation("Apify: Starting download of {ItemCount} items for dataset: {DataSetId}", items.Count, dataSetId);

        var downloadTasks = items.Select(async (item, index) =>
        {
            if (item == null)
            {
                logger.LogWarning("Apify: Skipping null item at index {Index} for dataset: {DataSetId}", index, dataSetId);
                return;
            }

            var json = item.ToString();
            if (string.IsNullOrWhiteSpace(json))
            {
                logger.LogWarning("Apify: Skipping empty item at index {Index} for dataset: {DataSetId}", index, dataSetId);
                return;
            }

            try
            {
                var fileName = $"{dataSetId}{FileNameSeparator}{index + 1}{JsonFileExtension}";
                using var stream = GenerateStreamFromString(json);
                await storage.WriteDataItemAsync(fileName, stream, cancellationToken);

                logger.LogDebug("Apify: Successfully downloaded item {Index} for dataset: {DataSetId}", index + 1, dataSetId);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Apify: Failed to download item {Index} for dataset: {DataSetId}. JSON: {Json}",
                    index + 1, dataSetId, json);
                throw; // Re-throw to allow caller to handle the error appropriately
            }
        });

        try
        {
            await Task.WhenAll(downloadTasks);
            logger.LogInformation("Apify: Download completed successfully for {ItemCount} items in dataset: {DataSetId}",
                items.Count, dataSetId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Apify: Download failed for dataset: {DataSetId} with error: {ErrorMessage}",
                dataSetId, ex.Message);
            throw;
        }
    }

    public async Task<List<WebScraperDatasetItem>> GetWebScraperDatasetsAsync(string dataSetId, List<object> items, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(dataSetId);
        ArgumentNullException.ThrowIfNull(items);

        if (items.Count == 0)
        {
            logger.LogInformation("Apify: No items to process for web scraper dataset: {DataSetId}", dataSetId);
            return new List<WebScraperDatasetItem>();
        }

        logger.LogInformation("Apify: Starting processing of {ItemCount} items for web scraper dataset: {DataSetId}",
            items.Count, dataSetId);

        var jsonSerializerOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        var webScraperDataSetItems = new List<WebScraperDatasetItem>();
        var processingTasks = items.Select(async (item, index) =>
        {
            if (item == null)
            {
                logger.LogWarning("Apify: Skipping null item at index {Index} for web scraper dataset: {DataSetId}",
                    index, dataSetId);
                return (WebScraperDatasetItem?)null;
            }

            var json = item.ToString();
            if (string.IsNullOrWhiteSpace(json))
            {
                logger.LogWarning("Apify: Skipping empty item at index {Index} for web scraper dataset: {DataSetId}",
                    index, dataSetId);
                return (WebScraperDatasetItem?)null;
            }

            try
            {
                using var stream = GenerateStreamFromString(json);
                var datasetItem = await JsonSerializer.DeserializeAsync<WebScraperDatasetItem>(
                    stream,
                    jsonSerializerOptions,
                    cancellationToken
                );

                if (datasetItem == null)
                {
                    logger.LogWarning("Apify: Failed to deserialize item {Index} for web scraper dataset: {DataSetId}. JSON: {Json}",
                        index + 1, dataSetId, json);
                    return (WebScraperDatasetItem?)null;
                }

                logger.LogDebug("Apify: Successfully processed item {Index} for web scraper dataset: {DataSetId}",
                    index + 1, dataSetId);
                return datasetItem;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Apify: Failed to process item {Index} for web scraper dataset: {DataSetId}. JSON: {Json}",
                    index + 1, dataSetId, json);
                throw; // Re-throw to allow caller to handle the error appropriately
            }
        });

        try
        {
            var results = await Task.WhenAll(processingTasks);
            webScraperDataSetItems.AddRange(results.Where(item => item != null)!);

            logger.LogInformation("Apify: Successfully processed {ProcessedCount} out of {TotalCount} items for web scraper dataset: {DataSetId}",
                webScraperDataSetItems.Count, items.Count, dataSetId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Apify: Processing failed for web scraper dataset: {DataSetId} with error: {ErrorMessage}",
                dataSetId, ex.Message);
            throw;
        }

        return webScraperDataSetItems;
    }

    private static MemoryStream GenerateStreamFromString(string content)
    {
        ArgumentException.ThrowIfNullOrEmpty(content);

        var bytes = Encoding.UTF8.GetBytes(content);
        return new MemoryStream(bytes);
    }
}
