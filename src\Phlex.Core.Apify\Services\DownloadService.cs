﻿using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Constants;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using System.Text.Json;
using System.Web;

namespace Phlex.Core.Apify.Services;

public class DownloadService(ILogger<DownloadService> logger) : IDownloadService
{
    public async Task BinaryDownloadAsync(HttpClient client, string requestTemplateUrl, string[] keys, IDownloadStorage storage, CancellationToken cancellationToken = default)
    {
        try
        {
            foreach (var key in keys)
            {
                if (!DownloadConstants.ExcludeFiles.Any(key.ToUpper().Contains))
                {
                    var requestUrl = requestTemplateUrl.Replace("##key##", key);
                    using (var stream = await client.GetStreamAsync(requestUrl, cancellationToken))
                    {
                        await storage.WriteDataItemAsync(HttpUtility.UrlEncode(key), stream, cancellationToken);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Apify: An error occurred: {ErrorMessage}", ex.Message);
        }
    }

    public async Task DownloadItemsAsync(string dataSetId, List<object> items, IDownloadStorage storage, CancellationToken cancellationToken = default)
    {
        int count = 1;
        foreach (var item in items)
        {
            if (item != null)
            {
                string? json = item.ToString();
                try
                {
                    if (json != null)
                    {
                        using (var stream = GenerateStreamFromString(json))
                        {
                            await storage.WriteDataItemAsync(dataSetId + "_" + count + ".json", stream, cancellationToken);
                        }
                    }

                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Apify: An error occurred downloading item: {Json}", json);
                }
            }
            count++;
        }
    }

    public async Task<List<WebScraperDatasetItem>> GetWebScraperDatasetsAsync(string dataSetId, List<object> items, CancellationToken cancellationToken = default)
    {
        int count = 1;
        var webScraperDataSetItems = new List<WebScraperDatasetItem>();
        foreach (var item in items)
        {
            if (item != null)
            {
                string? json = item.ToString();
                try
                {
                    if (json != null)
                    {
                        using var stream = GenerateStreamFromString(json);
                        JsonSerializerOptions jsonSerializerOptions = new()
                        {
                            PropertyNameCaseInsensitive = true
                        };
                        JsonSerializerOptions options = jsonSerializerOptions;
                        var datasetItem = await JsonSerializer.DeserializeAsync<WebScraperDatasetItem>(
                            stream,
                            options,
                            cancellationToken
                        );

                        if (datasetItem != null)
                            webScraperDataSetItems.Add(datasetItem);
                        else
                            throw new InvalidDataException("Apify: Failed to deserialize JSON into WebScraperDatasetItem.");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Apify: An error occurred parsing item: {Json}", json);
                }
            }
            count++;
        }

        return webScraperDataSetItems;
    }

    private static MemoryStream GenerateStreamFromString(string s)
    {
        var stream = new MemoryStream();
        var writer = new StreamWriter(stream);
        writer.Write(s);
        writer.Flush();
        stream.Position = 0;
        return stream;
    }
}
