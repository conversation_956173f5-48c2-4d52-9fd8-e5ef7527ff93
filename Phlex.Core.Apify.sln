﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35527.113
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{12530907-E690-47C3-9CD8-2436D971C5F8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "sdk", "sdk", "{6CC363BD-B9B1-408D-8D2E-3BB39328CE1B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{5C901111-334B-41B8-8D58-7C39ED211BC4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "openapi", "openapi", "{5C5EB45A-E825-43A1-A890-4E063C811145}"
	ProjectSection(SolutionItems) = preProject
		openapi\crawler-openapi.json = openapi\crawler-openapi.json
		openapi\openapi.json = openapi\openapi.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{0B2BBD2C-73B7-49EE-92D9-0726000CD03E}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		azure-pipelines.yml = azure-pipelines.yml
		README.md = README.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Phlex.Core.Apify.IntegrationTests", "test\Phlex.Core.Apify.IntegrationTests\Phlex.Core.Apify.IntegrationTests.csproj", "{0ACDA5C1-7D23-4B5C-0546-B31A01C7FF8F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Apify.SDK", "src\sdk\Apify.SDK\Apify.SDK.csproj", "{0CF6CF92-8EFE-BDB4-1FEB-0F0F239BEAC4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Phlex.Core.Apify", "src\Phlex.Core.Apify\Phlex.Core.Apify.csproj", "{96CBBDF6-3288-FA1C-99A8-9A3C5E969FC3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "testConsole", "testConsole", "{A5072ADD-FD0F-4BE8-A0E0-8BE194C678E1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ConsoleTest", "ConsoleTest\ConsoleTest.csproj", "{F741093F-E2D3-403A-BEC7-8A5DA3AB3333}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Phlex.Core.Apify.Webhook", "src\Phlex.Core.Apify.Webhook\Phlex.Core.Apify.Webhook.csproj", "{7BD19761-FE09-42D9-A8FF-07DD012C3984}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Phlex.Core.Apify.Tests", "test\Phlex.Core.Apify.Tests\Phlex.Core.Apify.Tests.csproj", "{F28DD89C-E186-43A2-8268-6D7D1D4BC00C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0ACDA5C1-7D23-4B5C-0546-B31A01C7FF8F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0ACDA5C1-7D23-4B5C-0546-B31A01C7FF8F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0ACDA5C1-7D23-4B5C-0546-B31A01C7FF8F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0ACDA5C1-7D23-4B5C-0546-B31A01C7FF8F}.Release|Any CPU.Build.0 = Release|Any CPU
		{0CF6CF92-8EFE-BDB4-1FEB-0F0F239BEAC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0CF6CF92-8EFE-BDB4-1FEB-0F0F239BEAC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0CF6CF92-8EFE-BDB4-1FEB-0F0F239BEAC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0CF6CF92-8EFE-BDB4-1FEB-0F0F239BEAC4}.Release|Any CPU.Build.0 = Release|Any CPU
		{96CBBDF6-3288-FA1C-99A8-9A3C5E969FC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{96CBBDF6-3288-FA1C-99A8-9A3C5E969FC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{96CBBDF6-3288-FA1C-99A8-9A3C5E969FC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{96CBBDF6-3288-FA1C-99A8-9A3C5E969FC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{F741093F-E2D3-403A-BEC7-8A5DA3AB3333}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F741093F-E2D3-403A-BEC7-8A5DA3AB3333}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F741093F-E2D3-403A-BEC7-8A5DA3AB3333}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F741093F-E2D3-403A-BEC7-8A5DA3AB3333}.Release|Any CPU.Build.0 = Release|Any CPU
		{7BD19761-FE09-42D9-A8FF-07DD012C3984}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7BD19761-FE09-42D9-A8FF-07DD012C3984}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7BD19761-FE09-42D9-A8FF-07DD012C3984}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7BD19761-FE09-42D9-A8FF-07DD012C3984}.Release|Any CPU.Build.0 = Release|Any CPU
		{F28DD89C-E186-43A2-8268-6D7D1D4BC00C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F28DD89C-E186-43A2-8268-6D7D1D4BC00C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F28DD89C-E186-43A2-8268-6D7D1D4BC00C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F28DD89C-E186-43A2-8268-6D7D1D4BC00C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{6CC363BD-B9B1-408D-8D2E-3BB39328CE1B} = {12530907-E690-47C3-9CD8-2436D971C5F8}
		{0ACDA5C1-7D23-4B5C-0546-B31A01C7FF8F} = {5C901111-334B-41B8-8D58-7C39ED211BC4}
		{0CF6CF92-8EFE-BDB4-1FEB-0F0F239BEAC4} = {6CC363BD-B9B1-408D-8D2E-3BB39328CE1B}
		{96CBBDF6-3288-FA1C-99A8-9A3C5E969FC3} = {12530907-E690-47C3-9CD8-2436D971C5F8}
		{F741093F-E2D3-403A-BEC7-8A5DA3AB3333} = {A5072ADD-FD0F-4BE8-A0E0-8BE194C678E1}
		{7BD19761-FE09-42D9-A8FF-07DD012C3984} = {12530907-E690-47C3-9CD8-2436D971C5F8}
		{F28DD89C-E186-43A2-8268-6D7D1D4BC00C} = {5C901111-334B-41B8-8D58-7C39ED211BC4}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {30552673-F30D-4D97-B3F5-E6E98EE13F7B}
	EndGlobalSection
EndGlobal
