using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using Phlex.Core.Apify.Services;
using Xunit;

namespace Phlex.Core.Apify.Tests;

public class DownloadServiceTests
{
    private readonly ILogger<DownloadService> _logger;
    private readonly IDownloadStorage _storage;
    private readonly DownloadService _downloadService;

    public DownloadServiceTests()
    {
        _logger = Substitute.For<ILogger<DownloadService>>();
        _storage = Substitute.For<IDownloadStorage>();
        _downloadService = new DownloadService(_logger);
    }

    [Fact]
    public async Task BinaryDownloadAsync_ValidKey_WritesToStorage()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "validKey" };
        _storage.WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>())
                .Returns(Task.CompletedTask);

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage);

        // Assert
        await _storage.Received(1).WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task BinaryDownloadAsync_ExcludedKey_DoesNotWriteToStorage()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "CRAWLEE_STATE" };

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage);

        // Assert
        await _storage.DidNotReceive().WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task DownloadItemsAsync_ValidItems_WritesToStorage()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { new { Name = "Item1" }, new { Name = "Item2" } };
        _storage.WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>())
                .Returns(Task.CompletedTask);

        // Act
        await _downloadService.DownloadItemsAsync(dataSetId, items, _storage);

        // Assert
        await _storage.Received(2).WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task DownloadItemsAsync_EmptyItems_DoesNotWriteToStorage()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object>();

        // Act
        await _downloadService.DownloadItemsAsync(dataSetId, items, _storage);

        // Assert
        await _storage.DidNotReceive().WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_ValidItems_ReturnsDatasetItems()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object>
            {
                JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.com", text = "Item1" }),
                JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.org", text = "Item2" })
            };

        // Act
        var result = await _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.Count);
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_InvalidJson_ReturnsEmptyAndThrowsException()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { "Invalid JSON" };

        // Act & Assert
        await Assert.ThrowsAsync<JsonException>(() =>
            _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None));

        // Verify error logging occurred
        _logger.Received().Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Failed to process item")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task BinaryDownloadAsync_Exception_ThrowsAndLogsError()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler { ThrowException = true });
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "validKey" };

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(() =>
            _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage));

        // Verify error logging occurred
        _logger.Received().Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Binary download failed")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task DownloadItemsAsync_Exception_ThrowsAndLogsError()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { new { Name = "Item1" } };
        _storage.WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>())
                .Throws(new Exception("Test exception"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() =>
            _downloadService.DownloadItemsAsync(dataSetId, items, _storage));

        // Verify error logging occurred
        _logger.Received().Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Download failed for dataset")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    #region New Comprehensive Tests

    [Fact]
    public async Task BinaryDownloadAsync_NullClient_ThrowsArgumentNullException()
    {
        // Arrange
        HttpClient client = null!;
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "validKey" };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage));
    }

    [Fact]
    public async Task BinaryDownloadAsync_NullOrEmptyUrl_ThrowsArgumentException()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string[] keys = { "validKey" };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _downloadService.BinaryDownloadAsync(client, "", keys, _storage));

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.BinaryDownloadAsync(client, null!, keys, _storage));
    }

    [Fact]
    public async Task BinaryDownloadAsync_UrlWithoutPlaceholder_ThrowsArgumentException()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/noplaceholder";
        string[] keys = { "validKey" };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage));

        Assert.Contains("##key##", exception.Message);
    }

    [Fact]
    public async Task BinaryDownloadAsync_NullKeys_ThrowsArgumentNullException()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = null!;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage));
    }

    [Fact]
    public async Task BinaryDownloadAsync_NullStorage_ThrowsArgumentNullException()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "validKey" };
        IDownloadStorage storage = null!;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, storage));
    }

    [Fact]
    public async Task BinaryDownloadAsync_EmptyKeysArray_LogsInformationAndReturns()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = Array.Empty<string>();

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage);

        // Assert
        await _storage.DidNotReceive().WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
        _logger.Received().Log(
            LogLevel.Information,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("No valid keys found")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task BinaryDownloadAsync_AllKeysExcluded_LogsInformationAndReturns()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "CRAWLEE_STATE", "INPUT", "SDK_CRAWLER_STATISTICS_1" }; // All excluded keys

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage);

        // Assert
        await _storage.DidNotReceive().WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
        _logger.Received().Log(
            LogLevel.Information,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("No valid keys found")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task DownloadItemsAsync_NullDataSetId_ThrowsArgumentNullException()
    {
        // Arrange
        string dataSetId = null!;
        var items = new List<object> { new { Name = "Item1" } };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.DownloadItemsAsync(dataSetId, items, _storage));
    }

    [Fact]
    public async Task DownloadItemsAsync_EmptyDataSetId_ThrowsArgumentException()
    {
        // Arrange
        string dataSetId = "";
        var items = new List<object> { new { Name = "Item1" } };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _downloadService.DownloadItemsAsync(dataSetId, items, _storage));
    }

    [Fact]
    public async Task DownloadItemsAsync_NullItems_ThrowsArgumentNullException()
    {
        // Arrange
        string dataSetId = "testDataSet";
        List<object> items = null!;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.DownloadItemsAsync(dataSetId, items, _storage));
    }

    [Fact]
    public async Task DownloadItemsAsync_NullStorage_ThrowsArgumentNullException()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { new { Name = "Item1" } };
        IDownloadStorage storage = null!;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.DownloadItemsAsync(dataSetId, items, storage));
    }

    [Fact]
    public async Task DownloadItemsAsync_ItemsWithNullValues_SkipsNullItems()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { new { Name = "Item1" }, null!, new { Name = "Item2" } };
        _storage.WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>())
                .Returns(Task.CompletedTask);

        // Act
        await _downloadService.DownloadItemsAsync(dataSetId, items, _storage);

        // Assert
        await _storage.Received(2).WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
        _logger.Received().Log(
            LogLevel.Warning,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Skipping null item")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_NullDataSetId_ThrowsArgumentNullException()
    {
        // Arrange
        string dataSetId = null!;
        var items = new List<object> { JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.com", text = "Item1" }) };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None));
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_EmptyDataSetId_ThrowsArgumentException()
    {
        // Arrange
        string dataSetId = "";
        var items = new List<object> { JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.com", text = "Item1" }) };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None));
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_NullItems_ThrowsArgumentNullException()
    {
        // Arrange
        string dataSetId = "testDataSet";
        List<object> items = null!;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None));
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_EmptyItems_ReturnsEmptyList()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object>();

        // Act
        var result = await _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None);

        // Assert
        Assert.Empty(result);
        _logger.Received().Log(
            LogLevel.Information,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("No items to process")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_ItemsWithNullValues_SkipsNullItems()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object>
        {
            JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.com", text = "Item1" }),
            null!,
            JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.org", text = "Item2" })
        };

        // Act
        var result = await _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.Count);
        _logger.Received().Log(
            LogLevel.Warning,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Skipping null item")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_ItemsWithEmptyStrings_SkipsEmptyItems()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object>
        {
            JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.com", text = "Item1" }),
            "",
            JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.org", text = "Item2" })
        };

        // Act
        var result = await _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.Count);
        _logger.Received().Log(
            LogLevel.Warning,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Skipping empty item")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_MixedValidAndInvalidJson_ProcessesValidItems()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object>
        {
            JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.com", text = "Item1" }),
            "Invalid JSON",
            JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.org", text = "Item2" })
        };

        // Act & Assert
        await Assert.ThrowsAsync<JsonException>(() =>
            _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None));
    }

    [Fact]
    public async Task BinaryDownloadAsync_MultipleValidKeys_ProcessesAllKeys()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "key1", "key2", "key3" };
        _storage.WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>())
                .Returns(Task.CompletedTask);

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage);

        // Assert
        await _storage.Received(3).WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
        _logger.Received().Log(
            LogLevel.Information,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Starting binary download for 3 keys")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task BinaryDownloadAsync_MixedValidAndExcludedKeys_ProcessesOnlyValidKeys()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "validKey1", "CRAWLEE_STATE", "validKey2", "INPUT" };
        _storage.WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>())
                .Returns(Task.CompletedTask);

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storage);

        // Assert
        await _storage.Received(2).WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<CancellationToken>());
        _logger.Received().Log(
            LogLevel.Information,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Starting binary download for 2 keys")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    #endregion
}

public class MockHttpMessageHandler : HttpMessageHandler
{
    public bool ThrowException { get; set; }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        if (ThrowException)
        {
            throw new HttpRequestException("Test exception");
        }

        var response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
        {
            Content = new StreamContent(new MemoryStream(Encoding.UTF8.GetBytes("test data")))
        };
        return await Task.FromResult(response);
    }
}
