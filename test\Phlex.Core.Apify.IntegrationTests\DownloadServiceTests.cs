using System.Text;
using System.Text.Json;
using Moq;
using Phlex.Core.Apify.IntegrationTests.Utils;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using Phlex.Core.Apify.Services;

namespace Phlex.Core.Apify.IntegrationTests;

public class DownloadServiceTests
{
    private readonly TestLogger<DownloadService> _logger;
    private readonly Mock<IDownloadStorage> _storageMock;
    private readonly DownloadService _downloadService;

    public DownloadServiceTests()
    {
        _logger = new TestLogger<DownloadService>();
        _storageMock = new Mock<IDownloadStorage>();
        _downloadService = new DownloadService(_logger);
    }

    [Fact]
    public async Task BinaryDownloadAsync_ValidKey_WritesToStorage()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "validKey" };
        _storageMock.Setup(s => s.WriteDataItemAsync(It.IsAny<string>(), It.IsAny<Stream>(), It.IsAny<CancellationToken>()))
                    .Returns(Task.CompletedTask);

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storageMock.Object);

        // Assert
        _storageMock.Verify(s => s.WriteDataItemAsync(It.IsAny<string>(), It.IsAny<Stream>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task BinaryDownloadAsync_ExcludedKey_DoesNotWriteToStorage()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "CRAWLEE_STATE" }; 

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storageMock.Object);

        // Assert
        _storageMock.Verify(s => s.WriteDataItemAsync(It.IsAny<string>(), It.IsAny<Stream>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task DownloadItemsAsync_ValidItems_WritesToStorage()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { new { Name = "Item1" }, new { Name = "Item2" } };
        _storageMock.Setup(s => s.WriteDataItemAsync(It.IsAny<string>(), It.IsAny<Stream>(), It.IsAny<CancellationToken>()))
                    .Returns(Task.CompletedTask);

        // Act
        await _downloadService.DownloadItemsAsync(dataSetId, items, _storageMock.Object);

        // Assert
        _storageMock.Verify(s => s.WriteDataItemAsync(It.IsAny<string>(), It.IsAny<Stream>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task DownloadItemsAsync_NullItems_DoesNotWriteToStorage()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { };

        // Act
        await _downloadService.DownloadItemsAsync(dataSetId, items, _storageMock.Object);

        // Assert
        _storageMock.Verify(s => s.WriteDataItemAsync(It.IsAny<string>(), It.IsAny<Stream>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_ValidItems_ReturnsDatasetItems()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object>
            {
                JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.com", text = "Item1" }),
                JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.org", text = "Item2" })
            };

        // Act
        var result = await _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.Count);
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_InvalidJson_LogsError()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { "Invalid JSON" };

        // Act
        var result = await _downloadService.GetWebScraperDatasetsAsync(dataSetId, items, CancellationToken.None);

        // Assert
        Assert.Empty(result);
        Assert.Contains(_logger.Logs, log => log.Message!.Contains("An error occurred parsing item"));
    }

    [Fact]
    public async Task BinaryDownloadAsync_Exception_LogsError()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler { ThrowException = true });
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "validKey" };

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storageMock.Object);

        // Assert
        Assert.Contains(_logger.Logs, log => log.Message!.Contains("An error occurred"));
    }

    [Fact]
    public async Task DownloadItemsAsync_Exception_LogsError()
    {
        // Arrange
        string dataSetId = "testDataSet";
        var items = new List<object> { new { Name = "Item1" } };
        _storageMock.Setup(s => s.WriteDataItemAsync(It.IsAny<string>(), It.IsAny<Stream>(), It.IsAny<CancellationToken>()))
                    .Throws(new Exception("Test exception"));

        // Act
        await _downloadService.DownloadItemsAsync(dataSetId, items, _storageMock.Object);

        // Assert
        Assert.Contains(_logger.Logs, log => log.Message!.Contains("An error occurred downloading item"));
    }
}

public class MockHttpMessageHandler : HttpMessageHandler
{
    public bool ThrowException { get; set; }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        if (ThrowException)
        {
            throw new HttpRequestException("Test exception");
        }

        var response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
        {
            Content = new StreamContent(new MemoryStream(Encoding.UTF8.GetBytes("test data")))
        };
        return await Task.FromResult(response);
    }
}
